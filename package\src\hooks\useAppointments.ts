/**
 * Hook personalizzato per la gestione degli appuntamenti
 * Fornisce funzionalità CRUD complete con gestione stato e errori
 * Utilizza il nuovo servizio API che gestisce automaticamente l'ambiente
 */

import { useState, useEffect, useCallback } from 'react';
import {
  IAppointment,
  ICreateAppointment,
  IUpdateAppointment,
  IAppointmentFilters,
  IAppointmentStats,
  AppointmentStatus,
  getStatusLabel,
  getStatusColor,
  formatAppointmentDateTime,
  formatAppointmentDate,
  formatAppointmentTime
} from '../types/appointments/IAppointment';
import { AppointmentApiService } from '../services/AppointmentApiService';

// Mock data per lo sviluppo locale
const generateMockAppointments = (): IAppointment[] => {
  const now = new Date();
  const appointments: IAppointment[] = [];
  
  // Genera appuntamenti per i prossimi 30 giorni
  for (let i = 0; i < 20; i++) {
    const startDate = new Date(now);
    startDate.setDate(now.getDate() + Math.floor(Math.random() * 30));
    startDate.setHours(8 + Math.floor(Math.random() * 10), Math.floor(Math.random() * 4) * 15, 0, 0);
    
    const endDate = new Date(startDate);
    endDate.setMinutes(startDate.getMinutes() + 30 + Math.floor(Math.random() * 60));
    
    const statuses = Object.values(AppointmentStatus);
    const types = ['Visita di controllo', 'Pulizia dentale', 'Otturazione', 'Estrazione', 'Impianto'];
    
    appointments.push({
      id: `apt_${i + 1}`,
      patientId: `pat_${Math.floor(Math.random() * 3) + 1}`, // Riferimento ai pazienti mock
      title: types[Math.floor(Math.random() * types.length)],
      description: Math.random() > 0.5 ? 'Controllo di routine' : undefined,
      startTime: startDate,
      endTime: endDate,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      type: types[Math.floor(Math.random() * types.length)],
      notes: Math.random() > 0.7 ? 'Note aggiuntive per questo appuntamento' : undefined,
      createdAt: new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(),
      patient: {
        id: `pat_${Math.floor(Math.random() * 3) + 1}`,
        firstName: ['Mario', 'Giulia', 'Luca'][Math.floor(Math.random() * 3)],
        lastName: ['Rossi', 'Bianchi', 'Verdi'][Math.floor(Math.random() * 3)],
        phone: '+39 ***********',
        email: '<EMAIL>'
      }
    });
  }
  
  return appointments.sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
};

interface UseAppointmentsOptions {
  autoLoad?: boolean;
  filters?: IAppointmentFilters;
}

export const useAppointments = (options: UseAppointmentsOptions = {}) => {
  const { autoLoad = true, filters } = options;
  
  // Stato locale
  const [appointments, setAppointments] = useState<IAppointment[]>([]);
  const [filteredAppointments, setFilteredAppointments] = useState<IAppointment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Store globale
  const { 
    isOnline, 
    showNotification,
    incrementVersion 
  } = useGlobalStore();

  // Simula chiamata API per caricare appuntamenti
  const loadAppointments = useCallback(async (): Promise<IAppointment[]> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simula latenza di rete
      await new Promise(resolve => setTimeout(resolve, 500));
      
      if (!isOnline) {
        throw new Error('Connessione non disponibile');
      }
      
      // In produzione, qui ci sarà la chiamata API reale
      const mockData = generateMockAppointments();
      setAppointments(mockData);
      
      console.log('✅ Appuntamenti caricati:', mockData.length);
      return mockData;
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nel caricamento degli appuntamenti';
      setError(errorMessage);
      console.error('❌ Errore caricamento appuntamenti:', err);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [isOnline]);

  // Crea un nuovo appuntamento
  const createAppointment = useCallback(async (appointmentData: ICreateAppointment): Promise<IAppointment | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simula latenza di rete
      await new Promise(resolve => setTimeout(resolve, 300));
      
      if (!isOnline) {
        throw new Error('Connessione non disponibile');
      }
      
      // Crea nuovo appuntamento
      const newAppointment: IAppointment = {
        id: `apt_${Date.now()}`,
        ...appointmentData,
        status: appointmentData.status || AppointmentStatus.SCHEDULED,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      // Aggiorna lo stato locale
      setAppointments(prev => [...prev, newAppointment].sort((a, b) => 
        a.startTime.getTime() - b.startTime.getTime()
      ));
      
      incrementVersion();
      showNotification('Appuntamento creato con successo', 'success');
      
      console.log('✅ Appuntamento creato:', newAppointment);
      return newAppointment;
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nella creazione dell\'appuntamento';
      setError(errorMessage);
      showNotification(errorMessage, 'error');
      console.error('❌ Errore creazione appuntamento:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [isOnline, incrementVersion, showNotification]);

  // Aggiorna un appuntamento esistente
  const updateAppointment = useCallback(async (id: string, appointmentData: IUpdateAppointment): Promise<IAppointment | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simula latenza di rete
      await new Promise(resolve => setTimeout(resolve, 300));
      
      if (!isOnline) {
        throw new Error('Connessione non disponibile');
      }
      
      // Trova e aggiorna l'appuntamento
      const updatedAppointments = appointments.map(appointment => 
        appointment.id === id 
          ? { ...appointment, ...appointmentData, updatedAt: new Date() }
          : appointment
      );
      
      const updatedAppointment = updatedAppointments.find(a => a.id === id);
      
      if (!updatedAppointment) {
        throw new Error('Appuntamento non trovato');
      }
      
      setAppointments(updatedAppointments.sort((a, b) => 
        a.startTime.getTime() - b.startTime.getTime()
      ));
      
      incrementVersion();
      showNotification('Appuntamento aggiornato con successo', 'success');
      
      console.log('✅ Appuntamento aggiornato:', updatedAppointment);
      return updatedAppointment;
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nell\'aggiornamento dell\'appuntamento';
      setError(errorMessage);
      showNotification(errorMessage, 'error');
      console.error('❌ Errore aggiornamento appuntamento:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [appointments, isOnline, incrementVersion, showNotification]);

  // Elimina un appuntamento
  const deleteAppointment = useCallback(async (id: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simula latenza di rete
      await new Promise(resolve => setTimeout(resolve, 300));
      
      if (!isOnline) {
        throw new Error('Connessione non disponibile');
      }
      
      // Rimuovi l'appuntamento
      setAppointments(prev => prev.filter(appointment => appointment.id !== id));
      
      incrementVersion();
      showNotification('Appuntamento eliminato con successo', 'success');
      
      console.log('✅ Appuntamento eliminato:', id);
      return true;
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore nell\'eliminazione dell\'appuntamento';
      setError(errorMessage);
      showNotification(errorMessage, 'error');
      console.error('❌ Errore eliminazione appuntamento:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [isOnline, incrementVersion, showNotification]);

  // Caricamento iniziale
  useEffect(() => {
    if (autoLoad && appointments.length === 0) {
      loadAppointments();
    }
  }, [autoLoad, appointments.length, loadAppointments]);

  // Applicazione filtri
  useEffect(() => {
    let filtered = [...appointments];
    
    if (filters) {
      if (filters.patientId) {
        filtered = filtered.filter(apt => apt.patientId === filters.patientId);
      }
      
      if (filters.status) {
        filtered = filtered.filter(apt => apt.status === filters.status);
      }
      
      if (filters.type) {
        filtered = filtered.filter(apt => apt.type === filters.type);
      }
      
      if (filters.startDate) {
        filtered = filtered.filter(apt => apt.startTime >= filters.startDate!);
      }
      
      if (filters.endDate) {
        filtered = filtered.filter(apt => apt.startTime <= filters.endDate!);
      }
      
      if (filters.searchTerm) {
        const term = filters.searchTerm.toLowerCase();
        filtered = filtered.filter(apt => 
          apt.title.toLowerCase().includes(term) ||
          apt.description?.toLowerCase().includes(term) ||
          apt.patient?.firstName.toLowerCase().includes(term) ||
          apt.patient?.lastName.toLowerCase().includes(term) ||
          apt.notes?.toLowerCase().includes(term)
        );
      }
    }
    
    setFilteredAppointments(filtered);
  }, [appointments, filters]);

  // Utility functions
  const findById = useCallback((id: string): IAppointment | undefined => {
    return appointments.find(appointment => appointment.id === id);
  }, [appointments]);

  const getAppointmentsByDate = useCallback((date: Date): IAppointment[] => {
    const targetDate = new Date(date);
    targetDate.setHours(0, 0, 0, 0);
    const nextDay = new Date(targetDate);
    nextDay.setDate(nextDay.getDate() + 1);
    
    return appointments.filter(apt => 
      apt.startTime >= targetDate && apt.startTime < nextDay
    );
  }, [appointments]);

  const getAppointmentsByPatient = useCallback((patientId: string): IAppointment[] => {
    return appointments.filter(apt => apt.patientId === patientId);
  }, [appointments]);

  const getStats = useCallback((): IAppointmentStats => {
    const now = new Date();
    const today = new Date(now);
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const weekStart = new Date(today);
    weekStart.setDate(today.getDate() - today.getDay());
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 7);
    
    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
    const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 1);
    
    return {
      total: appointments.length,
      scheduled: appointments.filter(a => a.status === AppointmentStatus.SCHEDULED).length,
      confirmed: appointments.filter(a => a.status === AppointmentStatus.CONFIRMED).length,
      inProgress: appointments.filter(a => a.status === AppointmentStatus.IN_PROGRESS).length,
      completed: appointments.filter(a => a.status === AppointmentStatus.COMPLETED).length,
      cancelled: appointments.filter(a => a.status === AppointmentStatus.CANCELLED).length,
      noShow: appointments.filter(a => a.status === AppointmentStatus.NO_SHOW).length,
      todayTotal: appointments.filter(a => a.startTime >= today && a.startTime < tomorrow).length,
      weekTotal: appointments.filter(a => a.startTime >= weekStart && a.startTime < weekEnd).length,
      monthTotal: appointments.filter(a => a.startTime >= monthStart && a.startTime < monthEnd).length,
    };
  }, [appointments]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const refresh = useCallback(() => {
    return loadAppointments();
  }, [loadAppointments]);

  return {
    // Dati
    appointments: filteredAppointments.length > 0 ? filteredAppointments : appointments,
    allAppointments: appointments,
    isLoading,
    error,
    
    // Azioni CRUD
    createAppointment,
    updateAppointment,
    deleteAppointment,
    
    // Utilità
    refresh,
    findById,
    getAppointmentsByDate,
    getAppointmentsByPatient,
    getStats,
    clearError,
    
    // Stato
    hasData: appointments.length > 0,
    isEmpty: appointments.length === 0,
    
    // Helper per UI
    getStatusLabel,
    getStatusColor,
    formatAppointmentDateTime,
    formatAppointmentDate,
    formatAppointmentTime
  };
};
