/**
 * Servizio API per la gestione degli appuntamenti
 * 
 * Questo servizio fornisce un'interfaccia unificata per le operazioni
 * sugli appuntamenti, gestendo automaticamente se utilizzare il database reale
 * o i dati mock in base all'ambiente.
 */

import { IAppointment, ICreateAppointment, AppointmentStatus } from '../types/appointments/IAppointment';

// Mock data per l'ambiente browser
const generateMockAppointments = (): IAppointment[] => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  
  return [
    {
      id: 'apt_1',
      patientId: 'patient_1',
      title: 'Visita di controllo',
      description: 'Controllo semestrale e pulizia dentale',
      startTime: new Date(today.getTime() + 9 * 60 * 60 * 1000), // 9:00 oggi
      endTime: new Date(today.getTime() + 10 * 60 * 60 * 1000), // 10:00 oggi
      status: AppointmentStatus.CONFIRMED,
      type: 'Visita di controllo',
      notes: 'Paziente puntuale',
      createdAt: new Date('2024-06-15'),
      updatedAt: new Date('2024-06-20'),
      patient: {
        id: 'patient_1',
        firstName: 'Mario',
        lastName: 'Rossi',
        phone: '+39 ***********',
        email: '<EMAIL>'
      }
    },
    {
      id: 'apt_2',
      patientId: 'patient_2',
      title: 'Controllo ortodontico',
      description: 'Verifica progressi trattamento ortodontico',
      startTime: new Date(today.getTime() + 24 * 60 * 60 * 1000 + 14 * 60 * 60 * 1000), // 14:00 domani
      endTime: new Date(today.getTime() + 24 * 60 * 60 * 1000 + 15 * 60 * 60 * 1000), // 15:00 domani
      status: AppointmentStatus.SCHEDULED,
      type: 'Ortodonzia',
      notes: 'Portare bite notturno',
      createdAt: new Date('2024-06-20'),
      updatedAt: new Date('2024-06-25'),
      patient: {
        id: 'patient_2',
        firstName: 'Giulia',
        lastName: 'Bianchi',
        phone: '+39 ***********',
        email: '<EMAIL>'
      }
    },
    {
      id: 'apt_3',
      patientId: 'patient_3',
      title: 'Otturazione',
      description: 'Otturazione molare superiore destro',
      startTime: new Date(today.getTime() + 2 * 24 * 60 * 60 * 1000 + 16 * 60 * 60 * 1000), // 16:00 dopodomani
      endTime: new Date(today.getTime() + 2 * 24 * 60 * 60 * 1000 + 17 * 60 * 60 * 1000), // 17:00 dopodomani
      status: AppointmentStatus.SCHEDULED,
      type: 'Otturazione',
      notes: 'Utilizzare anestesia locale',
      createdAt: new Date('2024-06-25'),
      updatedAt: new Date('2024-06-30'),
      patient: {
        id: 'patient_3',
        firstName: 'Luca',
        lastName: 'Verdi',
        phone: '+39 ***********',
        email: '<EMAIL>'
      }
    },
    {
      id: 'apt_4',
      patientId: 'patient_4',
      title: 'Seduta di sbiancamento',
      description: 'Prima seduta di sbiancamento dentale',
      startTime: new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000 + 10 * 60 * 60 * 1000), // 10:00 tra 3 giorni
      endTime: new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000 + 11.5 * 60 * 60 * 1000), // 11:30 tra 3 giorni
      status: AppointmentStatus.CONFIRMED,
      type: 'Sbiancamento',
      notes: 'Paziente ansiosa, necessita rassicurazione',
      createdAt: new Date('2024-06-28'),
      updatedAt: new Date('2024-07-01'),
      patient: {
        id: 'patient_4',
        firstName: 'Anna',
        lastName: 'Neri',
        phone: '+39 ***********',
        email: '<EMAIL>'
      }
    },
    {
      id: 'apt_5',
      patientId: 'patient_5',
      title: 'Controllo post-intervento',
      description: 'Controllo dopo estrazione dente del giudizio',
      startTime: new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000 + 15 * 60 * 60 * 1000), // 15:00 tra una settimana
      endTime: new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000 + 15.5 * 60 * 60 * 1000), // 15:30 tra una settimana
      status: AppointmentStatus.SCHEDULED,
      type: 'Controllo post-operatorio',
      notes: 'Verificare guarigione e rimuovere punti',
      createdAt: new Date('2024-06-30'),
      updatedAt: new Date('2024-07-01'),
      patient: {
        id: 'patient_5',
        firstName: 'Francesco',
        lastName: 'Blu',
        phone: '+39 ***********',
        email: '<EMAIL>'
      }
    }
  ];
};

let mockAppointments = generateMockAppointments();

export class AppointmentApiService {
  /**
   * Determina se siamo in ambiente server (Node.js) o browser
   */
  private static isServerEnvironment(): boolean {
    return typeof window === 'undefined';
  }

  /**
   * Simula latenza di rete
   */
  private static async simulateNetworkDelay(ms: number = 500): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Ottiene tutti gli appuntamenti
   */
  static async getAllAppointments(): Promise<IAppointment[]> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        // TODO: Implementare AppointmentDatabaseService
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: usa mock data
        await this.simulateNetworkDelay();
        console.log('📅 Caricamento appuntamenti (mock data):', mockAppointments.length);
        return [...mockAppointments].sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
      }
    } catch (error) {
      console.error('❌ Errore caricamento appuntamenti:', error);
      throw error;
    }
  }

  /**
   * Ottiene un appuntamento per ID
   */
  static async getAppointmentById(id: string): Promise<IAppointment | null> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: usa mock data
        await this.simulateNetworkDelay(300);
        const appointment = mockAppointments.find(a => a.id === id) || null;
        console.log('🔍 Appuntamento trovato (mock):', appointment?.id);
        return appointment;
      }
    } catch (error) {
      console.error('❌ Errore recupero appuntamento:', error);
      throw error;
    }
  }

  /**
   * Crea un nuovo appuntamento
   */
  static async createAppointment(appointmentData: ICreateAppointment): Promise<IAppointment> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: simula creazione
        await this.simulateNetworkDelay(800);
        
        const newAppointment: IAppointment = {
          id: `apt_${Date.now()}`,
          ...appointmentData,
          status: appointmentData.status || AppointmentStatus.SCHEDULED,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        mockAppointments.push(newAppointment);
        mockAppointments.sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
        
        console.log('✅ Appuntamento creato (mock):', newAppointment.id);
        return newAppointment;
      }
    } catch (error) {
      console.error('❌ Errore creazione appuntamento:', error);
      throw error;
    }
  }

  /**
   * Aggiorna un appuntamento esistente
   */
  static async updateAppointment(id: string, appointmentData: Partial<ICreateAppointment>): Promise<IAppointment> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: simula aggiornamento
        await this.simulateNetworkDelay(600);
        
        const appointmentIndex = mockAppointments.findIndex(a => a.id === id);
        if (appointmentIndex === -1) {
          throw new Error('Appuntamento non trovato');
        }
        
        const updatedAppointment = {
          ...mockAppointments[appointmentIndex],
          ...appointmentData,
          updatedAt: new Date()
        };
        
        mockAppointments[appointmentIndex] = updatedAppointment;
        mockAppointments.sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
        
        console.log('✅ Appuntamento aggiornato (mock):', updatedAppointment.id);
        return updatedAppointment;
      }
    } catch (error) {
      console.error('❌ Errore aggiornamento appuntamento:', error);
      throw error;
    }
  }

  /**
   * Elimina un appuntamento
   */
  static async deleteAppointment(id: string): Promise<boolean> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: simula eliminazione
        await this.simulateNetworkDelay(400);
        
        const appointmentIndex = mockAppointments.findIndex(a => a.id === id);
        if (appointmentIndex === -1) {
          throw new Error('Appuntamento non trovato');
        }
        
        mockAppointments.splice(appointmentIndex, 1);
        console.log('✅ Appuntamento eliminato (mock):', id);
        return true;
      }
    } catch (error) {
      console.error('❌ Errore eliminazione appuntamento:', error);
      throw error;
    }
  }

  /**
   * Ottiene appuntamenti per paziente
   */
  static async getAppointmentsByPatient(patientId: string): Promise<IAppointment[]> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: filtra mock data
        await this.simulateNetworkDelay(400);
        
        const results = mockAppointments
          .filter(appointment => appointment.patientId === patientId)
          .sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
        
        console.log(`🔍 Appuntamenti per paziente ${patientId} (mock):`, results.length);
        return results;
      }
    } catch (error) {
      console.error('❌ Errore ricerca appuntamenti per paziente:', error);
      throw error;
    }
  }

  /**
   * Ottiene appuntamenti per data
   */
  static async getAppointmentsByDate(date: Date): Promise<IAppointment[]> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: filtra mock data
        await this.simulateNetworkDelay(400);
        
        const targetDate = new Date(date);
        targetDate.setHours(0, 0, 0, 0);
        const nextDay = new Date(targetDate);
        nextDay.setDate(nextDay.getDate() + 1);
        
        const results = mockAppointments.filter(appointment => 
          appointment.startTime >= targetDate && appointment.startTime < nextDay
        ).sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
        
        console.log(`🔍 Appuntamenti per data ${date.toDateString()} (mock):`, results.length);
        return results;
      }
    } catch (error) {
      console.error('❌ Errore ricerca appuntamenti per data:', error);
      throw error;
    }
  }

  /**
   * Conta il numero totale di appuntamenti
   */
  static async getAppointmentCount(): Promise<number> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: conta mock data
        return mockAppointments.length;
      }
    } catch (error) {
      console.error('❌ Errore conteggio appuntamenti:', error);
      return 0;
    }
  }
}
