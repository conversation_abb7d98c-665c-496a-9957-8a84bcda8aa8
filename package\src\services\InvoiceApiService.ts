/**
 * Servizio API per la gestione delle fatture
 * 
 * Questo servizio fornisce un'interfaccia unificata per le operazioni
 * sulle fatture, gestendo automaticamente se utilizzare il database reale
 * o i dati mock in base all'ambiente.
 */

import { IInvoice, ICreateInvoice, InvoiceStatus } from '../types/invoices/IInvoice';

// Mock data per l'ambiente browser
const generateMockInvoices = (): IInvoice[] => {
  const now = new Date();
  
  return [
    {
      id: 'inv_1',
      patientId: 'patient_1',
      invoiceNumber: 'FAT-2024-001',
      issueDate: new Date(now.getFullYear(), now.getMonth(), 1),
      dueDate: new Date(now.getFullYear(), now.getMonth(), 31),
      amount: 150.00,
      taxAmount: 33.00,
      totalAmount: 183.00,
      status: InvoiceStatus.PAID,
      description: 'Visita di controllo e pulizia dentale',
      notes: 'Pagamento ricevuto in contanti',
      createdAt: new Date(now.getFullYear(), now.getMonth(), 1),
      updatedAt: new Date(now.getFullYear(), now.getMonth(), 5),
      patient: {
        id: 'patient_1',
        firstName: '<PERSON>',
        lastName: 'Rossi',
        email: '<EMAIL>',
        phone: '+39 ***********'
      }
    },
    {
      id: 'inv_2',
      patientId: 'patient_2',
      invoiceNumber: 'FAT-2024-002',
      issueDate: new Date(now.getFullYear(), now.getMonth(), 10),
      dueDate: new Date(now.getFullYear(), now.getMonth() + 1, 10),
      amount: 300.00,
      taxAmount: 66.00,
      totalAmount: 366.00,
      status: InvoiceStatus.SENT,
      description: 'Otturazione molare superiore',
      notes: 'Fattura inviata via email',
      createdAt: new Date(now.getFullYear(), now.getMonth(), 10),
      updatedAt: new Date(now.getFullYear(), now.getMonth(), 12),
      patient: {
        id: 'patient_2',
        firstName: 'Giulia',
        lastName: 'Bianchi',
        email: '<EMAIL>',
        phone: '+39 ***********'
      }
    },
    {
      id: 'inv_3',
      patientId: 'patient_3',
      invoiceNumber: 'FAT-2024-003',
      issueDate: new Date(now.getFullYear(), now.getMonth(), 15),
      dueDate: new Date(now.getFullYear(), now.getMonth() + 1, 15),
      amount: 800.00,
      taxAmount: 176.00,
      totalAmount: 976.00,
      status: InvoiceStatus.DRAFT,
      description: 'Impianto dentale con corona',
      notes: 'Bozza da completare',
      createdAt: new Date(now.getFullYear(), now.getMonth(), 15),
      updatedAt: new Date(now.getFullYear(), now.getMonth(), 20),
      patient: {
        id: 'patient_3',
        firstName: 'Luca',
        lastName: 'Verdi',
        email: '<EMAIL>',
        phone: '+39 ***********'
      }
    },
    {
      id: 'inv_4',
      patientId: 'patient_4',
      invoiceNumber: 'FAT-2024-004',
      issueDate: new Date(now.getFullYear(), now.getMonth(), 20),
      dueDate: new Date(now.getFullYear(), now.getMonth() - 1, 20), // Scaduta
      amount: 250.00,
      taxAmount: 55.00,
      totalAmount: 305.00,
      status: InvoiceStatus.OVERDUE,
      description: 'Sbiancamento dentale professionale',
      notes: 'Fattura scaduta, sollecitare pagamento',
      createdAt: new Date(now.getFullYear(), now.getMonth(), 20),
      updatedAt: new Date(now.getFullYear(), now.getMonth(), 25),
      patient: {
        id: 'patient_4',
        firstName: 'Anna',
        lastName: 'Neri',
        email: '<EMAIL>',
        phone: '+39 ***********'
      }
    },
    {
      id: 'inv_5',
      patientId: 'patient_5',
      invoiceNumber: 'FAT-2024-005',
      issueDate: new Date(now.getFullYear(), now.getMonth(), 25),
      dueDate: new Date(now.getFullYear(), now.getMonth() + 1, 25),
      amount: 450.00,
      taxAmount: 99.00,
      totalAmount: 549.00,
      status: InvoiceStatus.CONFIRMED,
      description: 'Estrazione dente del giudizio',
      notes: 'Intervento completato con successo',
      createdAt: new Date(now.getFullYear(), now.getMonth(), 25),
      updatedAt: new Date(now.getFullYear(), now.getMonth(), 28),
      patient: {
        id: 'patient_5',
        firstName: 'Francesco',
        lastName: 'Blu',
        email: '<EMAIL>',
        phone: '+39 ***********'
      }
    }
  ];
};

let mockInvoices = generateMockInvoices();

export class InvoiceApiService {
  /**
   * Determina se siamo in ambiente server (Node.js) o browser
   */
  private static isServerEnvironment(): boolean {
    return typeof window === 'undefined';
  }

  /**
   * Simula latenza di rete
   */
  private static async simulateNetworkDelay(ms: number = 500): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Ottiene tutte le fatture
   */
  static async getAllInvoices(): Promise<IInvoice[]> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        // TODO: Implementare InvoiceDatabaseService
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: usa mock data
        await this.simulateNetworkDelay();
        console.log('💰 Caricamento fatture (mock data):', mockInvoices.length);
        return [...mockInvoices].sort((a, b) => b.issueDate.getTime() - a.issueDate.getTime());
      }
    } catch (error) {
      console.error('❌ Errore caricamento fatture:', error);
      throw error;
    }
  }

  /**
   * Ottiene una fattura per ID
   */
  static async getInvoiceById(id: string): Promise<IInvoice | null> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: usa mock data
        await this.simulateNetworkDelay(300);
        const invoice = mockInvoices.find(i => i.id === id) || null;
        console.log('🔍 Fattura trovata (mock):', invoice?.id);
        return invoice;
      }
    } catch (error) {
      console.error('❌ Errore recupero fattura:', error);
      throw error;
    }
  }

  /**
   * Crea una nuova fattura
   */
  static async createInvoice(invoiceData: ICreateInvoice): Promise<IInvoice> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: simula creazione
        await this.simulateNetworkDelay(800);
        
        // Calcola totali
        const taxAmount = invoiceData.taxAmount || (invoiceData.amount * 0.22);
        const totalAmount = invoiceData.amount + taxAmount;
        
        const newInvoice: IInvoice = {
          id: `inv_${Date.now()}`,
          ...invoiceData,
          taxAmount,
          totalAmount,
          status: invoiceData.status || InvoiceStatus.DRAFT,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        mockInvoices.push(newInvoice);
        mockInvoices.sort((a, b) => b.issueDate.getTime() - a.issueDate.getTime());
        
        console.log('✅ Fattura creata (mock):', newInvoice.id);
        return newInvoice;
      }
    } catch (error) {
      console.error('❌ Errore creazione fattura:', error);
      throw error;
    }
  }

  /**
   * Aggiorna una fattura esistente
   */
  static async updateInvoice(id: string, invoiceData: Partial<ICreateInvoice>): Promise<IInvoice> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: simula aggiornamento
        await this.simulateNetworkDelay(600);
        
        const invoiceIndex = mockInvoices.findIndex(i => i.id === id);
        if (invoiceIndex === -1) {
          throw new Error('Fattura non trovata');
        }
        
        // Ricalcola totali se l'importo è cambiato
        let taxAmount = invoiceData.taxAmount;
        let totalAmount = invoiceData.amount;
        
        if (invoiceData.amount && !invoiceData.taxAmount) {
          taxAmount = invoiceData.amount * 0.22;
        }
        
        if (invoiceData.amount && taxAmount) {
          totalAmount = invoiceData.amount + taxAmount;
        }
        
        const updatedInvoice = {
          ...mockInvoices[invoiceIndex],
          ...invoiceData,
          ...(taxAmount && { taxAmount }),
          ...(totalAmount && { totalAmount }),
          updatedAt: new Date()
        };
        
        mockInvoices[invoiceIndex] = updatedInvoice;
        mockInvoices.sort((a, b) => b.issueDate.getTime() - a.issueDate.getTime());
        
        console.log('✅ Fattura aggiornata (mock):', updatedInvoice.id);
        return updatedInvoice;
      }
    } catch (error) {
      console.error('❌ Errore aggiornamento fattura:', error);
      throw error;
    }
  }

  /**
   * Elimina una fattura
   */
  static async deleteInvoice(id: string): Promise<boolean> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: simula eliminazione
        await this.simulateNetworkDelay(400);
        
        const invoiceIndex = mockInvoices.findIndex(i => i.id === id);
        if (invoiceIndex === -1) {
          throw new Error('Fattura non trovata');
        }
        
        mockInvoices.splice(invoiceIndex, 1);
        console.log('✅ Fattura eliminata (mock):', id);
        return true;
      }
    } catch (error) {
      console.error('❌ Errore eliminazione fattura:', error);
      throw error;
    }
  }

  /**
   * Ottiene fatture per paziente
   */
  static async getInvoicesByPatient(patientId: string): Promise<IInvoice[]> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: filtra mock data
        await this.simulateNetworkDelay(400);
        
        const results = mockInvoices
          .filter(invoice => invoice.patientId === patientId)
          .sort((a, b) => b.issueDate.getTime() - a.issueDate.getTime());
        
        console.log(`🔍 Fatture per paziente ${patientId} (mock):`, results.length);
        return results;
      }
    } catch (error) {
      console.error('❌ Errore ricerca fatture per paziente:', error);
      throw error;
    }
  }

  /**
   * Conta il numero totale di fatture
   */
  static async getInvoiceCount(): Promise<number> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        throw new Error('Database service not implemented yet');
      } else {
        // Ambiente browser: conta mock data
        return mockInvoices.length;
      }
    } catch (error) {
      console.error('❌ Errore conteggio fatture:', error);
      return 0;
    }
  }
}
