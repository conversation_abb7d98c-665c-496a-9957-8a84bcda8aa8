/**
 * Servizio API per la gestione dei pazienti
 * 
 * Questo servizio fornisce un'interfaccia unificata per le operazioni
 * sui pazienti, gestendo automaticamente se utilizzare il database reale
 * o i dati mock in base all'ambiente.
 */

import { IPatient, ICreatePatient } from '../types/patients/IPatient';

// Mock data per l'ambiente browser
const generateMockPatients = (): IPatient[] => {
  return [
    {
      id: 'patient_1',
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      email: '<EMAIL>',
      phone: '+39 ***********',
      dateOfBirth: new Date('1980-05-15'),
      address: 'Via Roma 123',
      city: 'Milano',
      postalCode: '20100',
      fiscalCode: '****************',
      notes: 'Paziente regolare, controlli ogni 6 mesi',
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-06-15')
    },
    {
      id: 'patient_2',
      firstName: 'Giulia',
      lastName: 'Bianchi',
      email: '<EMAIL>',
      phone: '+39 ***********',
      dateOfBirth: new Date('1992-08-22'),
      address: 'Via Verdi 456',
      city: 'Roma',
      postalCode: '00100',
      fiscalCode: '****************',
      notes: 'Trattamento ortodontico in corso',
      createdAt: new Date('2024-02-10'),
      updatedAt: new Date('2024-07-01')
    },
    {
      id: 'patient_3',
      firstName: 'Luca',
      lastName: 'Verdi',
      email: '<EMAIL>',
      phone: '+39 ***********',
      dateOfBirth: new Date('1975-12-03'),
      address: 'Corso Italia 789',
      city: 'Torino',
      postalCode: '10100',
      fiscalCode: '****************',
      notes: 'Allergia al lattice, utilizzare guanti nitrile',
      createdAt: new Date('2024-03-05'),
      updatedAt: new Date('2024-06-20')
    },
    {
      id: 'patient_4',
      firstName: 'Anna',
      lastName: 'Neri',
      email: '<EMAIL>',
      phone: '+39 ***********',
      dateOfBirth: new Date('1988-04-18'),
      address: 'Piazza Duomo 12',
      city: 'Firenze',
      postalCode: '50100',
      fiscalCode: '****************',
      notes: 'Paziente ansiosa, necessita sedazione',
      createdAt: new Date('2024-04-12'),
      updatedAt: new Date('2024-06-30')
    },
    {
      id: 'patient_5',
      firstName: 'Francesco',
      lastName: 'Blu',
      email: '<EMAIL>',
      phone: '+39 ***********',
      dateOfBirth: new Date('1965-11-30'),
      address: 'Via Nazionale 567',
      city: 'Napoli',
      postalCode: '80100',
      fiscalCode: '****************',
      notes: 'Diabetico, prestare attenzione alle infezioni',
      createdAt: new Date('2024-05-08'),
      updatedAt: new Date('2024-06-25')
    }
  ];
};

let mockPatients = generateMockPatients();

export class PatientApiService {
  /**
   * Determina se siamo in ambiente server (Node.js) o browser
   */
  private static isServerEnvironment(): boolean {
    return typeof window === 'undefined';
  }

  /**
   * Simula latenza di rete
   */
  private static async simulateNetworkDelay(ms: number = 500): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Ottiene tutti i pazienti
   */
  static async getAllPatients(): Promise<IPatient[]> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        const { PatientDatabaseService } = await import('./PatientDatabaseService');
        return await PatientDatabaseService.getAllPatients();
      } else {
        // Ambiente browser: usa mock data
        await this.simulateNetworkDelay();
        console.log('📊 Caricamento pazienti (mock data):', mockPatients.length);
        return [...mockPatients];
      }
    } catch (error) {
      console.error('❌ Errore caricamento pazienti:', error);
      throw error;
    }
  }

  /**
   * Ottiene un paziente per ID
   */
  static async getPatientById(id: string): Promise<IPatient | null> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        const { PatientDatabaseService } = await import('./PatientDatabaseService');
        return await PatientDatabaseService.getPatientById(id);
      } else {
        // Ambiente browser: usa mock data
        await this.simulateNetworkDelay(300);
        const patient = mockPatients.find(p => p.id === id) || null;
        console.log('🔍 Paziente trovato (mock):', patient?.id);
        return patient;
      }
    } catch (error) {
      console.error('❌ Errore recupero paziente:', error);
      throw error;
    }
  }

  /**
   * Crea un nuovo paziente
   */
  static async createPatient(patientData: ICreatePatient): Promise<IPatient> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        const { PatientDatabaseService } = await import('./PatientDatabaseService');
        return await PatientDatabaseService.createPatient(patientData);
      } else {
        // Ambiente browser: simula creazione
        await this.simulateNetworkDelay(800);
        
        const newPatient: IPatient = {
          id: `patient_${Date.now()}`,
          ...patientData,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        mockPatients.push(newPatient);
        console.log('✅ Paziente creato (mock):', newPatient.id);
        return newPatient;
      }
    } catch (error) {
      console.error('❌ Errore creazione paziente:', error);
      throw error;
    }
  }

  /**
   * Aggiorna un paziente esistente
   */
  static async updatePatient(id: string, patientData: Partial<ICreatePatient>): Promise<IPatient> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        const { PatientDatabaseService } = await import('./PatientDatabaseService');
        return await PatientDatabaseService.updatePatient(id, patientData);
      } else {
        // Ambiente browser: simula aggiornamento
        await this.simulateNetworkDelay(600);
        
        const patientIndex = mockPatients.findIndex(p => p.id === id);
        if (patientIndex === -1) {
          throw new Error('Paziente non trovato');
        }
        
        const updatedPatient = {
          ...mockPatients[patientIndex],
          ...patientData,
          updatedAt: new Date()
        };
        
        mockPatients[patientIndex] = updatedPatient;
        console.log('✅ Paziente aggiornato (mock):', updatedPatient.id);
        return updatedPatient;
      }
    } catch (error) {
      console.error('❌ Errore aggiornamento paziente:', error);
      throw error;
    }
  }

  /**
   * Elimina un paziente
   */
  static async deletePatient(id: string): Promise<boolean> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        const { PatientDatabaseService } = await import('./PatientDatabaseService');
        return await PatientDatabaseService.deletePatient(id);
      } else {
        // Ambiente browser: simula eliminazione
        await this.simulateNetworkDelay(400);
        
        const patientIndex = mockPatients.findIndex(p => p.id === id);
        if (patientIndex === -1) {
          throw new Error('Paziente non trovato');
        }
        
        mockPatients.splice(patientIndex, 1);
        console.log('✅ Paziente eliminato (mock):', id);
        return true;
      }
    } catch (error) {
      console.error('❌ Errore eliminazione paziente:', error);
      throw error;
    }
  }

  /**
   * Cerca pazienti per query
   */
  static async searchPatients(query: string): Promise<IPatient[]> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        const { PatientDatabaseService } = await import('./PatientDatabaseService');
        return await PatientDatabaseService.searchPatients(query);
      } else {
        // Ambiente browser: simula ricerca
        await this.simulateNetworkDelay(400);
        
        const searchTerm = query.toLowerCase();
        const results = mockPatients.filter(patient => 
          patient.firstName.toLowerCase().includes(searchTerm) ||
          patient.lastName.toLowerCase().includes(searchTerm) ||
          patient.email?.toLowerCase().includes(searchTerm) ||
          patient.phone?.includes(searchTerm) ||
          patient.fiscalCode?.toLowerCase().includes(searchTerm)
        );
        
        console.log(`🔍 Ricerca pazienti (mock) per "${query}":`, results.length);
        return results;
      }
    } catch (error) {
      console.error('❌ Errore ricerca pazienti:', error);
      throw error;
    }
  }

  /**
   * Conta il numero totale di pazienti
   */
  static async getPatientCount(): Promise<number> {
    try {
      if (this.isServerEnvironment()) {
        // Ambiente server: usa il database reale
        const { PatientDatabaseService } = await import('./PatientDatabaseService');
        return await PatientDatabaseService.getPatientCount();
      } else {
        // Ambiente browser: conta mock data
        return mockPatients.length;
      }
    } catch (error) {
      console.error('❌ Errore conteggio pazienti:', error);
      return 0;
    }
  }
}
